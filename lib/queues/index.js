import {generateId} from "../utils/generateId";
import {DateTime} from "luxon";
import {params} from "@ampt/sdk";
import {mongoConnect} from "../database/mongoConnect.js";
import {docmqConfig} from "../config/docmq-config.js";


let queuesCollectionName = params('COLLECTION_DATA_BROKER_QUEUE') ? params('COLLECTION_DATA_BROKER_QUEUE') : 'databroker_queues'
export const insertNewQueue = async (payload, taskName, queueName = null, additionalPayload = {}, status = 'pending') => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const queue = queueName && queueName !== '' ? queueName : queuesCollectionName
    const queueId = generateId()
    const queueInsert = {
        _id: queueId,
        payload: payload,
        status: status,
        task: taskName,
        onQueueError: '',
        created_at: DateTime.utc(),
        ...additionalPayload
    }
    await db.collection(queue).insertOne(queueInsert);
    return queueId;
}

export const insertBulkQueues = async (queues, queueName) => {
    if (!queueName) {
        return;
    }
    const {db} = await mongoConnect()
    await db.collection(queueName).insertMany(queues);
}

export const runQueuePendingTask = async () => {
    // await queueTasks.run();
}

export const findQueue = async (filter) => {
    const {db} = await mongoConnect()
    return await db.collection(queuesCollectionName).find(filter).sort({created_at: 1}).limit(1).next();
}

export const updateQueue = async (queueId, payload) => {
    const {db} = await mongoConnect()
    await db.collection(queuesCollectionName).updateOne({_id: queueId}, {$set: payload});
}

export const updateQueueByFilter = async (filter, payload) => {
    const {db} = await mongoConnect()
    await db.collection(queuesCollectionName).updateOne(filter, {$set: payload});
}
