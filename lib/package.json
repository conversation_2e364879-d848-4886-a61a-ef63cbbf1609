{"name": "@conversionfinder/lib", "version": "1.0.0", "description": "Librería compartida para stream-docmq producer y worker", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "watch": "tsc --watch", "create-package": "npm pack"}, "keywords": ["stream-docmq", "shared", "library", "producer", "worker"], "author": "Stream DocMQ Team", "license": "MIT", "dependencies": {"@slack/web-api": "^7.9.2", "luxon": "^3.4.4", "dotenv": "^16.3.1", "mongodb": "^6.0.0", "docmq": "^0.5.7", "uuid": "^11.1.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "tsx": "^4.0.0", "nodemon": "^3.0.0"}, "peerDependencies": {}, "publishConfig": {"main": "dist/index.js", "registry": "https://npm.pkg.github.com"}}