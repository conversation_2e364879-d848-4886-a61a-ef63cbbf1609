#!/usr/bin/env node


import {Queue} from 'docmq';
import {createDriver, createLogger, docmqConfig, getFileProcessingQueueConfig} from './docmq-config.js';
import {MongoClient} from 'mongodb';
import HttpServer from "./server.js";
import {DateTime} from 'luxon';
import {successCallbackTask} from "./functions/successCallbackTask.js";

class TaskProducer {
    constructor(mode = 'listener') {
        this.mode = mode;
        this.queueConfig = getFileProcessingQueueConfig();
        this.logger = createLogger('Producer');
        this.driver = null;
        this.queue = null;
        this.mongoClient = null;
        this.db = null;
        this.collections = null; // Cambiado: ahora es un array de colecciones
        this.changeStreams = []; // Cambiado: ahora es un array de change streams
        this.jobsCreated = 0;
        this.startTime = Date.now();
        this.httpServer = new HttpServer(this);
        this.orphanCheckTimer = null;
        this.traceMonitorTimer = null;  // NUEVO: Timer para verificación de traces
        // MongoDB configuration
        this.mongoUri = docmqConfig.driver.options.mongo.uri;
        this.databaseName = docmqConfig.driver.options.mongo.database;

        // Parsear múltiples colecciones separadas por comas
        const collectionsEnv = process.env.PROCESSING_TASKS_COLLECTION || 'file_processing_tasks';
        this.collectionNames = collectionsEnv.split(',').map(name => name.trim()).filter(name => name.length > 0);

        this.logger.info(`🗄️ Colecciones configuradas: ${this.collectionNames.join(', ')}`);
    }

    async initialize() {
        this.logger.info('Inicializando producer/listener...');

        try {
            // Crear driver y cola DocMQ
            this.driver = await createDriver();
            this.queue = new Queue(this.driver, this.queueConfig.name, this.queueConfig.options);

            // Conectar a MongoDB para escuchar cambios
            this.mongoClient = new MongoClient(this.mongoUri);
            await this.mongoClient.connect();
            this.db = this.mongoClient.db(this.databaseName);

            // Inicializar múltiples colecciones
            this.collections = this.collectionNames.map(name => this.db.collection(name));

            this.logger.success('Producer conectado a MongoDB y DocMQ');
            this.showProducerInfo();

        } catch (error) {
            this.logger.error('Error inicializando producer:', error.message);
            throw error;
        }
    }

    showProducerInfo() {
        const orphanInterval = parseInt(process.env.ORPHAN_CHECK_INTERVAL) || 10 * 60 * 1000;
        const traceInterval = parseInt(process.env.TRACE_MONITOR_INTERVAL) || 30000;

        console.log('\n' + '='.repeat(70));
        console.log(`📨 PRODUCER/LISTENER - MONGODB CHANGE STREAMS`);
        console.log('='.repeat(70));
        console.log(`📦 Cola DocMQ: ${this.queueConfig.name}`);
        console.log(`🗄️  Colecciones MongoDB: ${this.collectionNames.join(', ')}`);
        console.log(`📊 Total colecciones: ${this.collectionNames.length}`);
        console.log(`🎯 Modo: ${this.mode}`);
        console.log(`⏱️  Iniciado: ${new Date().toLocaleString()}`);
        console.log(`🕐 Verificación huérfanos: cada ${orphanInterval / 60000} min`);
        console.log(`🔍 Verificación traces: cada ${traceInterval / 1000}s`);
        console.log('='.repeat(70));

        console.log(`🔍 Escuchando inserts en ${this.collectionNames.length} colecciones:`);
        this.collectionNames.forEach((name, index) => {
            console.log(`   ${index + 1}. ${name}`);
        });
        console.log('📋 Por cada insert → Genera job automáticamente');
        console.log('🔧 Verificación automática de jobs huérfanos en todas las colecciones');
        console.log('🎯 Presiona Ctrl+C para salir');

        console.log('='.repeat(70) + '\n');
    }

    async startListening() {
        try {
            await this.httpServer.start();
            this.logger.info('🔍 Iniciando escucha de cambios en MongoDB...');

            const pipeline = [
                {
                    $match: {
                        'operationType': 'insert'
                    }
                }
            ];

            // Crear change streams para cada colección
            for (let i = 0; i < this.collections.length; i++) {
                const collection = this.collections[i];
                const collectionName = this.collectionNames[i];

                const changeStream = collection.watch(pipeline, {
                    fullDocument: 'updateLookup'
                });

                changeStream.on('change', async (change) => {
                    await this.handleInsert(change, collectionName);
                });

                changeStream.on('error', (error) => {
                    this.logger.error(`Error en change stream para ${collectionName}:`, error.message);
                });

                this.changeStreams.push(changeStream);
                this.logger.success(`✅ Escuchando inserts en ${collectionName}`);
            }

            this.logger.success(`🎯 Todos los change streams iniciados para ${this.collectionNames.length} colecciones`);

            // Iniciar verificación de jobs huérfanos cada 10 minutos
            this.startOrphanCheck();

            // Iniciar monitoreo de traces cada 30 segundos
            this.startTraceMonitoring();

        } catch (error) {
            this.logger.error('Error iniciando change streams:', error.message);
            throw error;
        }
    }

    // build
    async handleInsert(change, collectionName) {
        try {
            const insertedDocument = change.fullDocument;
            const documentId = insertedDocument._id;
            this.logger.info(`📥 Nuevo insert detectado en ${collectionName}: ${documentId}`);
            // Crear job en DocMQ basado en el documento insertado
            const job = await this.createJobFromDocument(insertedDocument, collectionName);
            this.jobsCreated++;
            this.logger.success(`✅ Job creado desde ${collectionName}. Total jobs: ${this.jobsCreated}`);
            // Mostrar estadísticas cada 10 jobs
            if (this.jobsCreated % 10 === 0) {
                this.showStats();
            }

        } catch (error) {
            this.logger.error(`Error procesando insert desde ${collectionName}:`, error.message);
        }
    }

    async createJobFromDocument(document, collectionName) {
        // Preparar datos del job basado en el documento de MongoDB
        const jobData = {
            // Datos originales del documento
            originalDocument: document,

            // Datos específicos para el procesamiento
            _id: document._id,
            status: 'pending',

            // Metadatos adicionales
            metadata: {
                ...document.metadata,
                source: 'mongodb-insert',
                sourceCollection: collectionName, // NUEVO: Agregar información de la colección origen
                detectedAt: new Date().toISOString(),
                producer: 'listener'
            }
        };

        try {
            // Crear job en DocMQ usando la API correcta
            const jobRef = `doc_${document._id}_${Date.now()}`;

            // API CORRECTA de DocMQ: { ref, payload } - SIN repetición
            await this.queue.enqueue({
                ref: jobRef,
                payload: jobData,
                runEvery: null, // IMPORTANTE: No repetir el job
                retries: 3      // Cambiar a 3 retries (0 puede causar problemas)
            });

            this.logger.info(`✅ Job enqueued con ref: ${jobRef} desde ${collectionName}`);

            return {id: jobRef, ref: jobRef};

        } catch (error) {
            this.logger.error(`Error creando job en DocMQ desde ${collectionName}:`, error.message);
            throw error;
        }
    }


    startOrphanCheck() {
        const intervalMs = parseInt(process.env.ORPHAN_CHECK_INTERVAL) || 10 * 60 * 1000; // 10 minutos

        this.orphanCheckTimer = setInterval(async () => {
            try {
                await this.checkAndFixOrphanedJobs();
            } catch (error) {
                this.logger.error(`❌ Error verificando jobs huérfanos: ${error.message}`);
            }
        }, intervalMs);

        this.logger.info(`🕐 Verificación de jobs huérfanos iniciada (cada ${intervalMs / 60000} min)`);
    }

    async checkCompletedTraces() {
        try {
            this.logger.info('🔍 Verificando traces pendientes...');

            const databrokerCollection = process.env.DATABROKER_QUEUES_COLLECTION || 'databroker_queues';

            // 1. Buscar traces pending que han comenzado (completedJobs >= 1) para marcar como 'in_progress'
            const startedTraces = await this.db.collection(databrokerCollection).find({
                status: 'pending',
                completedJobs: {$gte: 1}
            }).toArray();

            let inProgressCount = 0;
            for (const trace of startedTraces) {
                const result = await this.db.collection(databrokerCollection).updateOne(
                    {_id: trace._id, status: 'pending'},
                    {
                        $set: {
                            status: 'in_progress',
                            started_at: new Date()
                        }
                    }
                );

                if (result.modifiedCount > 0) {
                    inProgressCount++;
                    this.logger.info(`🔄 Trace ${trace._id} marcado como in_progress (${trace.completedJobs}/${trace.totalJobs})`);
                }
            }

            // 2. Buscar traces in_progress donde completedJobs >= totalJobs para marcar como 'completed'
            const completedTraces = await this.db.collection(databrokerCollection).find({
                status: 'in_progress',
                $expr: {$gte: ['$completedJobs', '$totalJobs']}
            }).toArray();

            let completedCount = 0;
            for (const trace of completedTraces) {
                const result = await this.db.collection(databrokerCollection).updateOne(
                    {_id: trace._id, status: 'in_progress'},
                    {
                        $set: {
                            status: 'completed',
                            completed_at: new Date(),
                            processed_by: 'producer-monitor'
                        }
                    }
                );

                if (result.modifiedCount > 0) {
                    completedCount++;
                    this.logger.info(`✅ Trace ${trace._id} completado (${trace.completedJobs}/${trace.totalJobs})`);
                    await successCallbackTask(trace._id, {
                        totalModifiedCount: trace.totalModifiedCount,
                        totalUpsertedCount: trace.totalUpsertedCount
                    });
                }
            }

            // Resumen de cambios
            if (inProgressCount > 0 || completedCount > 0) {
                this.logger.info(`🎯 Traces actualizados: ${inProgressCount} in_progress, ${completedCount} completed`);
            } else {
                this.logger.info('✅ No hay traces para actualizar');
            }

        } catch (error) {
            this.logger.error(`❌ Error verificando traces: ${error.message}`);
        }
    }

    startTraceMonitoring() {
        const intervalMs = parseInt(process.env.TRACE_MONITOR_INTERVAL) || 30000; // 30 segundos

        this.traceMonitorTimer = setInterval(async () => {
            await this.checkCompletedTraces();
        }, intervalMs);

        this.logger.info(`🕐 Monitoreo de traces iniciado (cada ${intervalMs / 1000}s)`);
    }


    async checkAndFixOrphanedJobs() {
        try {
            this.logger.info('🔍 Verificando jobs huérfanos en todas las colecciones...');

            const docmqCollection = process.env.COLLECTION_QUEUES_NAME || 'docmq_jobs';

            // Calcular timestamp de hace 15 minutos
            const fifteenMinutesAgo = DateTime.now().minus({minutes: 15}).toISO();

            let totalOrphanedCount = 0;
            let totalRepairedCount = 0;

            // Verificar jobs huérfanos en cada colección
            for (const collectionName of this.collectionNames) {
                this.logger.info(`🔍 Verificando jobs huérfanos en ${collectionName}...`);

                // Buscar jobs en processing que llevan más de 15 minutos
                const stuckJobs = await this.db.collection(collectionName).find({
                    status: "processing",
                    processing_at: {$lt: fifteenMinutesAgo}
                }).toArray();

                this.logger.info(`Jobs en processing > 15 min en ${collectionName}: ${stuckJobs.length}`);

                if (stuckJobs.length === 0) {
                    this.logger.info(`✅ No hay jobs huérfanos en ${collectionName}`);
                    continue;
                }

                let orphanedCount = 0;
                let repairedCount = 0;

                for (const job of stuckJobs) {
                    const jobId = job._id;
                    const processingTime = DateTime.fromISO(job.processing_at);
                    const minutesStuck = Math.round(DateTime.now().diff(processingTime, 'minutes').minutes);

                    const docmqJob = await this.db.collection(docmqCollection).findOne({
                        ref: {$regex: `^doc_${jobId}_`}
                    });
                    this.logger.info(`Job ${jobId} en ${collectionName}: atascado ${minutesStuck} min, DocMQ: ${docmqJob ? 'existe' : 'NO EXISTE'}`);
                    orphanedCount++;
                    try {
                        await this.db.collection(collectionName).deleteOne({_id: jobId});
                        const cleanJob = {...job};
                        delete cleanJob.processing_at;
                        delete cleanJob.processed_by;
                        cleanJob.status = 'pending';
                        cleanJob.reprocessed_at = DateTime.utc().toISO();
                        cleanJob.reprocess_count = (job.reprocess_count || 0) + 1;

                        await this.db.collection(collectionName).insertOne(cleanJob);
                        repairedCount++;
                        this.logger.info(`🔧 Job huérfano liberado para reprocesamiento en ${collectionName}: ${jobId} (atascado ${minutesStuck} min, reintento ${cleanJob.reprocess_count})`);

                    } catch (error) {
                        if (error.code !== 11000) { // Ignorar duplicate key
                            this.logger.error(`❌ Error reparando job ${jobId} en ${collectionName}: ${error.message}`);
                        }
                    }
                }

                totalOrphanedCount += orphanedCount;
                totalRepairedCount += repairedCount;

                if (orphanedCount > 0) {
                    this.logger.warn(`🔧 Jobs huérfanos en ${collectionName}: ${orphanedCount}, reparados: ${repairedCount}`);
                }
            }

            // Resumen final
            if (totalOrphanedCount > 0) {
                this.logger.warn(`🎯 RESUMEN: Jobs huérfanos detectados: ${totalOrphanedCount}, reparados: ${totalRepairedCount} en ${this.collectionNames.length} colecciones`);
            } else {
                this.logger.info(`✅ No hay jobs huérfanos en ninguna de las ${this.collectionNames.length} colecciones`);
            }

        } catch (error) {
            this.logger.error(`❌ Error verificando jobs huérfanos: ${error.message}`);
        }
    }

    showStats() {
        const uptime = Math.floor((Date.now() - this.startTime) / 1000);
        const rate = this.jobsCreated > 0 ? (this.jobsCreated / uptime * 60).toFixed(2) : 0;

        console.log('\n' + '-'.repeat(50));
        console.log(`📊 ESTADÍSTICAS DEL PRODUCER`);
        console.log('-'.repeat(50));
        console.log(`📤 Jobs creados: ${this.jobsCreated}`);
        console.log(`⏱️  Tiempo activo: ${uptime}s`);
        console.log(`📈 Tasa: ${rate} jobs/min`);
        console.log(`🎯 Modo: ${this.mode}`);
        console.log(`🗄️  MongoDB: Conectado`);
        console.log('-'.repeat(50) + '\n');
    }

    async shutdown() {
        this.logger.info('🛑 Cerrando producer...');

        // Limpiar timer de verificación de huérfanos
        if (this.orphanCheckTimer) {
            clearInterval(this.orphanCheckTimer);
            this.orphanCheckTimer = null;
            this.logger.info('Timer de verificación de huérfanos detenido');
        }

        // Limpiar timer de monitoreo de traces
        if (this.traceMonitorTimer) {
            clearInterval(this.traceMonitorTimer);
            this.traceMonitorTimer = null;
            this.logger.info('Timer de monitoreo de traces detenido');
        }

        // Cerrar todos los change streams
        if (this.changeStreams && this.changeStreams.length > 0) {
            this.logger.info(`Cerrando ${this.changeStreams.length} change streams...`);
            for (let i = 0; i < this.changeStreams.length; i++) {
                try {
                    await this.changeStreams[i].close();
                    this.logger.info(`Change stream ${i + 1}/${this.changeStreams.length} cerrado (${this.collectionNames[i]})`);
                } catch (error) {
                    this.logger.error(`Error cerrando change stream para ${this.collectionNames[i]}:`, error.message);
                }
            }
            this.logger.success('Todos los change streams cerrados');
        }

        try {
            if (this.mongoClient) {
                await this.mongoClient.close();
            }
            if (this.queue) {
                await this.queue.close();
            }
            this.showStats();
            this.logger.success('Producer cerrado correctamente');
        } catch (error) {
            this.logger.error('Error durante el cierre:', error.message);
        }

        process.exit(0);
    }
}

// Manejo de argumentos de línea de comandos
const args = process.argv.slice(2);
const mode = args[0] || 'listener';

if (!['listener', 'manual'].includes(mode)) {
    console.error('❌ Modo inválido. Usar: listener, manual');
    process.exit(1);
}

// Crear y ejecutar producer
const producer = new TaskProducer(mode);


// Inicializar y ejecutar según el modo
producer.initialize().then(async () => {
    await producer.startListening();
}).catch(error => {
    console.error('❌ Error fatal iniciando producer:', error);
    process.exit(1);
});

export {TaskProducer};
