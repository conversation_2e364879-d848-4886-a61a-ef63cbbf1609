# MongoDB Configuration
MONGODB_URI=mongodb://root:<EMAIL>:55327,mdb7-1.conversionf-rzqpqx9ykknevckee.us-3.db.zcloud.ws:59932,mdb7-2.conversionf-rzqpqx9ykknevckee.us-3.db.zcloud.ws:59245/?authSource=admin&ssl=true&replicaSet=mdb7
MONGODB_DB_NAME=conversion_finder

# DocMQ Queue Configuration
COLLECTION_QUEUES_NAME=docmq_jobs_test
# Múltiples colecciones separadas por comas (sin espacios extra)
PROCESSING_TASKS_COLLECTION=file_processing_tasks_test,another_collection_test
DATABROKER_QUEUES_COLLECTION=databroker_queues_test

# Worker Configuration (used in queue config)
WORKER_COUNT=1

# API Server Configuration
PORT=3005

# Logging (optional)
LOG_LEVEL=info


DATA_BROKER_URL=https://creative-stack-mlrc0.ampt.app
DATA_BROKER_X_API_KEY=1
