# Configuración de Múltiples Colecciones

## 📋 Descripción

El producer ahora soporta escuchar múltiples colecciones de MongoDB simultáneamente, manteniendo toda la funcionalidad existente incluyendo:

- ✅ **startOrphanCheck** para todas las colecciones
- ✅ **startTraceMonitoring** (sin cambios)
- ✅ **Estadísticas y logging** detallado por colección
- ✅ **Manejo de errores** independiente por colección
- ✅ **Shutdown graceful** de todos los change streams

## 🔧 Configuración

### Formato de la Variable de Entorno

```bash
# Una sola colección (comportamiento anterior)
PROCESSING_TASKS_COLLECTION=file_processing_tasks_test

# Múltiples colecciones separadas por comas (NUEVO)
PROCESSING_TASKS_COLLECTION=file_processing_tasks_test,another_collection_test,third_collection

# Importante: NO usar espacios extra alrededor de las comas
```

### Ejemplos de Configuración

```bash
# Ejemplo 1: Dos colecciones
PROCESSING_TASKS_COLLECTION=file_processing_tasks_test,databroker_tasks_test

# Ejemplo 2: Tres colecciones
PROCESSING_TASKS_COLLECTION=collection1,collection2,collection3

# Ejemplo 3: Una sola colección (compatible con versión anterior)
PROCESSING_TASKS_COLLECTION=file_processing_tasks_test
```

## 🚀 Funcionalidades

### 1. **Change Streams Múltiples**
- Se crea un change stream independiente para cada colección
- Cada insert se procesa con información de la colección origen
- Los errores en una colección no afectan a las otras

### 2. **Verificación de Jobs Huérfanos**
- `startOrphanCheck()` ahora verifica jobs huérfanos en **todas** las colecciones configuradas
- Reporte detallado por colección y resumen general
- Misma lógica de 15 minutos para considerar jobs como huérfanos

### 3. **Logging Mejorado**
- Logs incluyen el nombre de la colección origen
- Estadísticas muestran total de colecciones monitoreadas
- Información detallada durante el startup y shutdown

### 4. **Metadatos Enriquecidos**
- Los jobs creados incluyen `sourceCollection` en los metadatos
- Facilita el debugging y tracking de origen de los jobs

## 📊 Ejemplo de Output

```
======================================================================
📨 PRODUCER/LISTENER - MONGODB CHANGE STREAMS
======================================================================
📦 Cola DocMQ: docmq_jobs_test
🗄️  Colecciones MongoDB: file_processing_tasks_test, another_collection_test
📊 Total colecciones: 2
🎯 Modo: listener
⏱️  Iniciado: 16/6/2025 10:30:00
🕐 Verificación huérfanos: cada 10 min
🔍 Verificación traces: cada 30s
======================================================================

🔍 Escuchando inserts en 2 colecciones:
   1. file_processing_tasks_test
   2. another_collection_test
📋 Por cada insert → Genera job automáticamente
🔧 Verificación automática de jobs huérfanos en todas las colecciones
🎯 Presiona Ctrl+C para salir

======================================================================
```

## 🔍 Logs de Ejemplo

```
[Producer] ✅ Escuchando inserts en file_processing_tasks_test
[Producer] ✅ Escuchando inserts en another_collection_test
[Producer] ✅ 🎯 Todos los change streams iniciados para 2 colecciones

[Producer] ℹ️  📥 Nuevo insert detectado en file_processing_tasks_test: 507f1f77bcf86cd799439011
[Producer] ✅ Job creado desde file_processing_tasks_test. Total jobs: 1

[Producer] ℹ️  🔍 Verificando jobs huérfanos en todas las colecciones...
[Producer] ℹ️  🔍 Verificando jobs huérfanos en file_processing_tasks_test...
[Producer] ℹ️  ✅ No hay jobs huérfanos en file_processing_tasks_test
[Producer] ℹ️  🔍 Verificando jobs huérfanos en another_collection_test...
[Producer] ℹ️  ✅ No hay jobs huérfanos en another_collection_test
[Producer] ℹ️  ✅ No hay jobs huérfanos en ninguna de las 2 colecciones
```

## ⚠️ Consideraciones Importantes

1. **Rendimiento**: Cada colección adicional consume recursos (change stream + verificaciones)
2. **Conexiones**: Se mantiene una sola conexión MongoDB pero múltiples change streams
3. **Compatibilidad**: 100% compatible con configuración de una sola colección
4. **Escalabilidad**: Recomendado máximo 5-10 colecciones por producer

## 🧪 Testing

Para probar la funcionalidad:

1. Configurar múltiples colecciones en `.env`
2. Iniciar el producer: `npm start`
3. Insertar documentos en cualquiera de las colecciones configuradas
4. Verificar que se crean jobs para cada insert
5. Verificar logs de orphan check en todas las colecciones

## 🔄 Migración

**Desde configuración de una sola colección:**
- No requiere cambios en el código
- Solo actualizar la variable de entorno `PROCESSING_TASKS_COLLECTION`
- Reiniciar el producer

**Rollback:**
- Cambiar `PROCESSING_TASKS_COLLECTION` a una sola colección
- Reiniciar el producer
