import _ from "lodash";
import {DateTime} from "luxon";
import {findOne} from "../../mongo/findOne.js";
import {Collections} from "../../mongo/collections.enum.js";
import {updateOne} from "../../mongo/updateOne.js";


export const verifyMajorOperation = async (configuration, mappedData) => {
    console.log('verifyMajorOperation v2')
    for (let item of mappedData) {
        const transfer = await findOne(Collections.Transfers, {
            consumer_phone: item.consumer_phone,
            client_id: item.client_id
        })
        if (!transfer || !transfer.completed_date) {
            console.log('transfer.completed_date is null');
            console.log('transfer id: ', transfer._id);
        }
        const maxDurationTransfer = _.maxBy(transfer.calls, 'duration');
        let completedDate = maxDurationTransfer?.completed_date || transfer.completed_date;

        if (!completedDate) {
            console.log('completedDate is null');
            console.log('transfer id:', transfer._id);
            console.log('maxDurationTransfer:', maxDurationTransfer);
        }
        try {
            if (typeof completedDate === 'string' && completedDate.trim() !== '') {
                completedDate = DateTime.fromISO(completedDate);
            }
        } catch (e) {
            console.log('Error parsing completedDate verifyMajorOperation:', e);
            completedDate = null;
        }
        await updateOne(Collections.Transfers, {_id: transfer._id}, {
            $set: {
                duration: maxDurationTransfer && maxDurationTransfer.duration ? maxDurationTransfer.duration : 0,
                completed_date: completedDate,
                call_id: maxDurationTransfer && maxDurationTransfer.call_id ? maxDurationTransfer.call_id : transfer.call_id
            }
        })
    }
    console.log('verifyMajorOperation end')
}

