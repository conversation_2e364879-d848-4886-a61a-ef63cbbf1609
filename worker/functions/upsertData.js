import {startCallbackSuccess} from "./callbacks/startCallbackSuccess";
import {upsert} from "./upsert.js";
import {addFilename} from "./utils/addFilename.js";

export const upsertData = async (collection, mappedData, mongoConfiguration, fileName, totalUpsertedCount, totalModifiedCount, mappingConfiguration) => {
    console.log('Collection: ', collection);
    console.log('mongoConfiguration: ', mongoConfiguration);
    const dbResult = await upsert(collection, mappedData, mongoConfiguration, true, fileName);
    await addFilename(mappedData, collection, fileName);
    const callbacks = mappingConfiguration.callbacks ? mappingConfiguration.callbacks : {}
    await startCallbackSuccess(callbacks?.success, mappedData);
    totalUpsertedCount = totalUpsertedCount ? totalUpsertedCount + dbResult?.upsertedCount : dbResult?.upsertedCount;
    totalModifiedCount = totalModifiedCount ? totalModifiedCount + dbResult?.modifiedCount : dbResult?.modifiedCount;
    console.log('dbResult.upsertedCount: ', totalUpsertedCount)
    console.log('dbResult.modifiedCount: ', totalModifiedCount)
    return {totalUpsertedCount, totalModifiedCount}
}
