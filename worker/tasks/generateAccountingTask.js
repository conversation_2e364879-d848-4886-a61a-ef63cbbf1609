import {DateTime} from "luxon";
import {getDecimalAmountByEventByLabel} from "../functions/accounting/getDecimalAmountByEventByLabel.js";
import {generateId} from "../functions/generateId.js";
import {getLeadId} from "../functions/accounting/getLeadId.js";
import {mongoConnect} from "../mongoConnect.js";
import {docmqConfig} from "../docmq-config.js";

export const generateAccountingTask = async (queue) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const {payload} = queue;
    let {
        collection,
        accountingType,
        items,
        invoiceAmountType,
        invoiceIsoWeekDateFilter,
        invoice_id,
        amounts,
        page,
        limit
    } = payload;
    const invoiceId = invoice_id;
    let pageSize = limit
    console.log(`Generate accounting start`)
    const filters = accountingType === 'REVENUE' ? {invoice_revenue_id: invoiceId} : {invoice_expense_id: invoiceId};
    console.log(`Page: ${page}`);
    console.log('filters: ');
    const itemsFound = await db.collection(collection).find(filters).limit(pageSize).skip(limit * page).toArray();
    console.log(`Items found ${itemsFound.length}`)
    const accountingArray = [];
    for (let amount of amounts) {
        const amountByEvent = getDecimalAmountByEventByLabel(invoiceAmountType, items, amount.amount);
        for (let item of itemsFound) {
            const _id = generateId()
            const accounting = {
                _id,
                lead_id: getLeadId(item, collection),
                source_table: collection,
                source_id: item._id,
                type: accountingType,
                vendor_id: item.vendor_id,
                client_id: item.client_id,
                tags: item.tags,
                amount: amountByEvent,
                invoice_id: invoiceId,
                created_at: DateTime.utc(),
                week: invoiceIsoWeekDateFilter?.week[0],
                year: invoiceIsoWeekDateFilter.year,
                month: invoiceIsoWeekDateFilter.month + 1,
                pubid: collection === 'transfers' ? item.pub_id : item.pubid,
                subid: collection === 'transfers' ? item.sub_id : item.subid,
                campaign_key: item.campaign_key,
                labelAmount: amount?.amountLabel
            }
            accountingArray.push(accounting);
        }
    }

    if (accountingArray && accountingArray.length > 0) {
        await db.collection('accounting').insertMany(accountingArray);
    }
    console.log(`generate accounting end`);
    return {result: 0}
}

