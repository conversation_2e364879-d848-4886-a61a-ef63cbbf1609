import Decimal from "decimal.js";
import {mongoConnect} from "../mongoConnect.js";
import {docmqConfig} from "../docmq-config.js";
import {transformDateFilters} from "../functions/transformDateFilters.js";
import {DateTime} from "luxon";
import _ from "lodash";
import {generateId, insertBulkQueues,insertNewQueue} from "@conversionfinder/stream-docmq";

export const generateInvoiceTask = async (queue) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const {payload} = queue;
    console.log('payload: ', payload)
    let {
        accountingType,
        collection,
        items,
        invoiceAmountType,
        amounts,
        invoiceIsoWeekDateFilter,
        invoiceLabel,
        invoice_id
    } = payload;
    let filters = buildFilters(payload);
    payload['filters'] = filters;
    console.log('inserting accounting queue')
    filters = transformDateFilters(filters);
    const accountingTypeFilter = accountingType === 'REVENUE' ? {invoice_revenue_id: null} : {invoice_expense_id: null};
    filters = {...filters, ...accountingTypeFilter}
    console.log(`Counting documents for accounting ${invoiceLabel}. ${DateTime.now()}`)
    const counted = await db.collection(collection).countDocuments(filters);
    console.log(`End counting documents for accounting ${invoiceLabel}. ${DateTime.now()}`)
    if (counted !== items) {
        console.log('Items changed')
        items = counted;
    }
    const totalAmount = getTotalAmount(invoiceAmountType, items, amounts);
    console.log('Total amount: ', totalAmount)
    const amountByItem = getAmountByItem(invoiceAmountType, items, amounts);
    const lineItems = getLineItems(amounts, items, invoiceAmountType)
    console.log('Amount by item: ', amountByItem);
    const invoiceId = invoice_id;
    const invoice = {
        _id: invoiceId,
        week: invoiceIsoWeekDateFilter?.week[0],
        year: invoiceIsoWeekDateFilter.year,
        month: invoiceIsoWeekDateFilter.month + 1,
        label: invoiceLabel,
        total_events: items,
        total_amount: totalAmount,
        amount_by_event: amountByItem,
        revenue: accountingType === 'REVENUE',
        invoice_type: accountingType,
        line_items: lineItems,
        created_at: DateTime.utc(),
        collection,
        invoiceAmountType
    }
    const update = accountingType === 'REVENUE' ? {invoice_revenue_id: invoiceId} : {invoice_expense_id: invoiceId};
    console.log(`updating ${collection} with invoice id: ${invoiceId}. ${DateTime.now()}`)
    await db.collection(collection).updateMany(filters, {$set: update});
    console.log(`Inserting invoice. ${DateTime.now()}`)
    await db.collection('invoices').insertOne(invoice)
    const totalPages = counted && counted > 0 ? new Decimal(counted).dividedBy(100).ceil().toNumber() : 0;
    console.log('totalPages: ', totalPages)
    const queueId = await insertNewQueue(payload, 'generateAccountingTask', null);
    const docMqTasks = _.times(totalPages, (page) => {
        const processId = generateId();
        return {
            _id: processId,
            payload: {...payload, page: page, limit: 100},
            task:'generateAccountingTask',
            status: 'pending',
            trace_id: queueId,
            created_at: DateTime.utc()
        };
    });
    docMqTasks && docMqTasks.length > 0 && await insertBulkQueues(docMqTasks, 'docmq_tasks');
    return {result: 0}
}


const buildFilters = (payload) => {
    const {
        collection, vendorId,
        pubId, dateFilters, campaignKey, accountingType, clientId
    } = payload;
    return {
        ...dateFilters,
        ...(collection === 'transfers' ? {pub_id: pubId} : {pubid: pubId}),
        campaign_key: campaignKey,
        ...(accountingType === 'REVENUE' && collection !== 'leads' ? {client_id: clientId} : {}),
        ...(accountingType !== 'REVENUE' ? {vendor_id: vendorId} : {}),
    };
}

const getLineItems = (amounts, items, invoiceAmountType) => {
    return _.map(amounts, item => {
        const amountDecimal = new Decimal(item.amount);
        const amount = amountDecimal.toDP(2).mul(100)
        const amountByEvent = invoiceAmountType === 'perItem' ? amount : amountDecimal.div(items).toDP(2).mul(100)
        const totalAmount = invoiceAmountType === 'perItem' ? amount.mul(items) : amount;
        return {
            label: item.amountLabel,
            amount_by_event: amountByEvent.toNumber(),
            total_amount: totalAmount.toNumber()
        }
    })
}

const getTotalAmount = (invoiceAmountType, items, amounts) => {
    const summaryTotal = _.sumBy(amounts, item => Number(item.amount));
    const amountDecimal = new Decimal(summaryTotal);
    const eventsDecimal = new Decimal(items)
    if (invoiceAmountType === 'all') {
        return amountDecimal.toDP(2).mul(100).toNumber();
    } else {
        return amountDecimal.mul(eventsDecimal).toDP(2).mul(100).toNumber()
    }
}

const getAmountByItem = (invoiceAmountType, items, amounts) => {
    const summaryTotal = _.sumBy(amounts, item => Number(item.amount));
    const amountDecimal = new Decimal(summaryTotal);
    const eventsDecimal = new Decimal(items)
    if (invoiceAmountType === 'all') {
        return amountDecimal.div(eventsDecimal).toDP(2).mul(100).toNumber();
    } else {
        return amountDecimal.toDP(2).mul(100).toNumber();
    }
}
