import {mongoConnect} from "../mongoConnect.js";
import {docmqConfig} from "../docmq-config.js";

const {db} = await mongoConnect(docmqConfig.database.database)
export const deleteInvoiceTask = async (queue) => {
    console.log('Deleting invoice task start');
    console.log('Queue: ', queue)
    const {payload} = queue;
    const {collection, row} = payload;
    const invoiceId = row._id
    console.log('Row id: ', invoiceId);
    console.log('collection: ', collection);
    console.log('Deleting accounting');
    await deleteAccounting(invoiceId);
    console.log(`Updating collection ${collection}`);
    await updateCollection(row.collection, invoiceId, row.invoice_type);
    return {result: 0}
}

const deleteAccounting = async (invoiceId) => {
    await db.collection('accounting').deleteMany({invoice_id: invoiceId});
}

const updateCollection = async (collection, invoiceId, invoice_type) => {
    const filter = invoice_type === 'REVENUE' ? {invoice_revenue_id: invoiceId} : {invoice_expense_id: invoiceId};
    console.log('filter: ', filter)
    const unset = invoice_type === 'REVENUE' ? {invoice_revenue_id: 1} : {invoice_expense_id: 1};
    console.log('unset: ', unset)
    await db.collection(collection).updateMany(filter, {$unset: unset})
}
