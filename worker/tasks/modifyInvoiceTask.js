import _ from "lodash";
import {DateTime} from "luxon";
import {generateId} from "@conversionfinder/stream-docmq";
import {getLeadId} from "../functions/accounting/getLeadId.js";
import {mongoConnect} from "../mongoConnect.js";
import {docmqConfig} from "../docmq-config.js";

const {db} = await mongoConnect(docmqConfig.database.database)

export const modifyInvoiceTask = async (queue) => {
    console.log('modifyInvoiceTask start')
    const {payload} = queue;
    const {modifiedFields, id: invoiceId, afterUpdate} = payload;
    const invoice = await db?.collection('invoices').findOne({_id: invoiceId});
    if (_.includes(modifiedFields, 'line_items')) {
        const updatedCollection = await getAffectedCollection(invoice);
        const filters = invoice.revenue === true ? {invoice_revenue_id: invoice._id} : {invoice_expense_id: invoice._id};
        const lineItems = invoice.line_items;
        await cleanAccounting(invoiceId);
        await addAccounting(lineItems, filters, updatedCollection, invoiceId, invoice);
        await deleteInvoiceLock(invoiceId)

    }
}

const deleteInvoiceLock = async (invoiceId) => {
    const deleteResult = await db.collection('update_collection_lock').deleteOne({
        collection: 'invoices',
        source_id: invoiceId
    })
    console.log('deleteResult: ', deleteResult)
}


const cleanAccounting = async (invoiceId) => {
    console.log(`Cleaning accounting ${invoiceId}`)
    await db.collection('accounting').deleteMany({invoice_id: invoiceId});
}

const addAccounting = async (lineItems, filters, updatedCollection, invoiceId, invoice) => {
    console.log(`Creating accounting for invoice ${invoiceId} with filters: ${JSON.stringify(filters)}`)
    for (let addedItem of lineItems) {
        let pageSize = 5000
        let page = 0;
        let hasNextPage = true;
        while (hasNextPage) {
            const bulkOperation = await db.collection(updatedCollection.collection).find(filters).limit(pageSize).skip(pageSize * page).map(item => {
                const _id = generateId();
                return {
                    _id,
                    lead_id: getLeadId(item, updatedCollection.collection),
                    source_table: updatedCollection.collection,
                    source_id: item._id,
                    type: invoice.revenue ? 'REVENUE' : 'EXPENSE',
                    vendor_id: item.vendor_id,
                    client_id: item.client_id,
                    tags: item.tags,
                    amount: addedItem.amount_by_event,
                    invoice_id: invoiceId,
                    created_at: DateTime.utc(),
                    week: invoice.week,
                    year: invoice.year,
                    month: invoice.month,
                    pubid: updatedCollection.collection === 'transfers' ? item.pub_id : item.pubid,
                    subid: updatedCollection.collection === 'transfers' ? item.sub_id : item.subid,
                    campaign_key: item.campaign_key,
                    labelAmount: addedItem?.label
                }
            }).toArray()
            if (bulkOperation.length < pageSize) {
                hasNextPage = false;
            }
            if (bulkOperation && bulkOperation.length > 0) {
                await db.collection('accounting').insertMany(bulkOperation);
            }
            page++;
        }
    }
}


const getAffectedCollection = async (invoice) => {
    const filter = invoice.revenue === true ? {invoice_revenue_id: invoice._id} : {invoice_expense_id: invoice._id};
    if (invoice.collection) {
        return {collection: invoice.collection};
    } else {
        const isLeads = await db.collection('leads').findOne(filter);
        if (isLeads != null) return {collection: 'leads'};
        const isTransfer = await db.collection('transfers').findOne(filter);
        if (isTransfer != null) return {collection: 'transfers'};
        const isPostbacks = await db.collection('postbacks').findOne(filter)
        if (isPostbacks != null) return {collection: 'postbacks'};
    }

}
