# MongoDB Configuration
MONGODB_URI=mongodb://root:<EMAIL>:55327,mdb7-1.conversionf-rzqpqx9ykknevckee.us-3.db.zcloud.ws:59932,mdb7-2.conversionf-rzqpqx9ykknevckee.us-3.db.zcloud.ws:59245/?authSource=admin&ssl=true&replicaSet=mdb7
MONGODB_DB_NAME=conversion_finder

# DocMQ Queue Configuration
COLLECTION_QUEUES_NAME=docmq_jobs_test
DATABROKER_QUEUES_COLLECTION=databroker_queues_test

# Worker Configuration ULTRA-MINIMALISTA
WORKER_COUNT=1
PORT=3006

# Node.js ULTRA-OPTIMIZADO para Docker
NODE_ENV=production
NODE_OPTIONS=--max-old-space-size=256 --gc-interval=1000
UV_THREADPOOL_SIZE=1

# DocMQ ULTRA-EFICIENTE
DOCMQ_POLL_INTERVAL=5000
DOCMQ_VISIBILITY=1800
DOCMQ_CONCURRENCY=1

# Logging para PRODUCCIÓN
LOG_LEVEL=info
LOG_FORMAT=json

# Alertas y SLA
MAX_PROCESSING_TIME_MINUTES=5
ERROR_RATE_THRESHOLD_PERCENT=5
HEALTH_CHECK_TIMEOUT_MS=5000


DATA_BROKER_URL=https://creative-stack-mlrc0.ampt.app
