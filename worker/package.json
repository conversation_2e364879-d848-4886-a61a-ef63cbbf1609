{"name": "@stream-docmq/worker", "version": "1.0.0", "description": "Load-balanced worker microservice for DocMQ queue processing", "main": "load-balanced-worker.js", "type": "module", "scripts": {"start": "bun load-balanced-worker.js", "dev": "bun run --watch load-balanced-worker.js", "prod": "node load-balanced-worker.js --instance=prod", "monitor": "node performance-monitor.js", "diagnose": "node diagnose-performance.js", "start:optimized": "NODE_OPTIONS='--max-old-space-size=512 --gc-interval=100 --expose-gc' UV_THREADPOOL_SIZE=4 node load-balanced-worker.js"}, "keywords": ["docmq", "worker", "queue", "load-balancing", "microservice"], "author": "Your Name", "license": "MIT", "dependencies": {"@hono/node-server": "1.14.3", "axios": "^1.9.0", "chalk": "^5.0.0", "decimal.js": "^10.4.3", "docmq": "^0.5.7", "dotenv": "^16.3.1", "fast-csv": "5.0.1", "lodash": "^4.17.21", "lru-cache": "^10.0.0", "luxon": "^3.4.4", "mongodb": "6.0.0", "radash": "^11.0.0", "uuid": "^11.1.0", "@conversionfinder/stream-docmq": "1.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/stream-docmq.git", "directory": "worker"}, "bugs": {"url": "https://github.com/your-username/stream-docmq/issues"}, "homepage": "https://github.com/your-username/stream-docmq/tree/main/worker#readme"}